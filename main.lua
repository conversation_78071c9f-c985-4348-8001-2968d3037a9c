--[[ Full script: Multi-select + improved reliability + Tabbed Interface (Shop + Miscellaneous)
   + English loading UI (no demo text)
   + Fake loading 2s BEFORE allowing main GUI to show
   + Do NOT show main GUI until both fake loading and initial populate finished
   + Added Pet Egg functionality with toggle buttons
]]
--// Services
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local MarketplaceService = game:GetService("MarketplaceService")
local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local UserInputService = game:GetService("UserInputService")
local TweenService = game:GetService("TweenService")

local LocalPlayer = Players.LocalPlayer
local PlayerGui = LocalPlayer:WaitForChild("PlayerGui")
local player = LocalPlayer

-- === Loading GUI (show immediately) ===
local LoadingGUI = Instance.new("ScreenGui")
LoadingGUI.Name = "DepsoAutoBuyLoading"
LoadingGUI.ResetOnSpawn = false
LoadingGUI.Parent = PlayerGui
LoadingGUI.ZIndexBehavior = Enum.ZIndexBehavior.Sibling

local loadingOverlay = Instance.new("Frame")
loadingOverlay.Size = UDim2.new(1, 0, 1, 0)
loadingOverlay.Position = UDim2.new(0, 0, 0, 0)
loadingOverlay.BackgroundColor3 = Color3.fromRGB(0,0,0)
loadingOverlay.BackgroundTransparency = 0.55
loadingOverlay.BorderSizePixel = 0
loadingOverlay.Parent = LoadingGUI

local loadingBox = Instance.new("Frame")
loadingBox.Size = UDim2.new(0, 520, 0, 160) -- bigger box
loadingBox.Position = UDim2.new(0.5, -260, 0.5, -80)
loadingBox.BackgroundColor3 = Color3.fromRGB(28,28,28)
loadingBox.BorderSizePixel = 0
loadingBox.Parent = LoadingGUI
local UICorner = Instance.new("UICorner", loadingBox)
UICorner.CornerRadius = UDim.new(0, 12)

local loadingTitle = Instance.new("TextLabel")
loadingTitle.Size = UDim2.new(1, -24, 0, 48)
loadingTitle.Position = UDim2.new(0, 12, 0, 8)
loadingTitle.BackgroundTransparency = 1
loadingTitle.Text = "Loading..."
loadingTitle.TextColor3 = Color3.fromRGB(250,250,250)
loadingTitle.Font = Enum.Font.GothamBold
loadingTitle.TextSize = 28
loadingTitle.TextXAlignment = Enum.TextXAlignment.Center
loadingTitle.Parent = loadingBox

local loadingLabel = Instance.new("TextLabel")
loadingLabel.Size = UDim2.new(1, -24, 0, 28)
loadingLabel.Position = UDim2.new(0, 12, 0, 62)
loadingLabel.BackgroundTransparency = 1
loadingLabel.Text = "Preparing UI — please wait"
loadingLabel.TextColor3 = Color3.fromRGB(200,200,200)
loadingLabel.Font = Enum.Font.Gotham
loadingLabel.TextSize = 16
loadingLabel.TextXAlignment = Enum.TextXAlignment.Center
loadingLabel.Parent = loadingBox

-- animated dots
local loadingRunning = true
task.spawn(function()
    local dots = 0
    while loadingRunning do
        dots = dots + 1
        if dots > 3 then dots = 0 end
        local s = string.rep(".", dots)
        pcall(function() loadingTitle.Text = "Loading" .. s end)
        task.wait(0.45)
    end
end)

-- fake loading duration (seconds)
local FAKE_LOADING_SECONDS = 2
local fakeDone = false
task.spawn(function()
    task.wait(FAKE_LOADING_SECONDS)
    fakeDone = true
end)

-- continue with main script while LoadingGUI still visible
local GameInfo = (pcall(function() return MarketplaceService:GetProductInfo(game.PlaceId) end) and MarketplaceService:GetProductInfo(game.PlaceId)) or { Name = "Grow a Garden" }

--// Data
local SeedStock = {}
local GearStock = {}
local PetEggStock = {}
local SelectedSeeds = {} -- SelectedSeeds[name] = true/false
local SelectedGear = {} -- SelectedGear[name] = true/false
local SelectedPetEggs = {} -- SelectedPetEggs[name] = true/false

--// Remotes
local GameEvents = ReplicatedStorage:WaitForChild("GameEvents")

-- Continuous Auto Farm System using RunService.Heartbeat
local AutoFarmHeartbeat = {
    connection = nil,
    lastScanTime = 0,
    scanThrottle = 0.01, -- Very fast scanning (10ms for continuous collection)
    lastHarvestTime = 0,
    harvestThrottle = 0.001, -- Almost no delay between harvests (1ms for instant collection)
    frameProcessCount = 0,
    performanceStats = {
        totalScans = 0,
        totalHarvests = 0,
        avgScanTime = 0,
        lastPerformanceCheck = 0
    }
}

local function BuySeed(Seed: string)
    pcall(function()
        GameEvents.BuySeedStock:FireServer(Seed)
    end)
end

local function BuyGear(Gear: string)
    pcall(function()
        GameEvents.BuyGearStock:FireServer(Gear)
    end)
end

local function BuyPetEgg(EggName: string)
    pcall(function()
        GameEvents.BuyPetEgg:FireServer(EggName)
    end)
end

-- Auto-sell functionality from reference script
local IsSelling = false
local function SellInventory()
    local Character = LocalPlayer.Character
    if not Character then return end

    local Previous = Character:GetPivot()

    -- Prevent conflict
    if IsSelling then return end
    IsSelling = true

    -- Move to sell location (coordinates from reference script)
    Character:PivotTo(CFrame.new(62, 4, -26))

    -- Sell inventory
    pcall(function()
        GameEvents.Sell_Inventory:FireServer()
    end)

    -- Wait a bit then return to previous position
    task.wait(0.5)
    Character:PivotTo(Previous)

    task.wait(0.2)
    IsSelling = false
end

-- Get inventory crops count
local function GetInvCrops()
    local Character = LocalPlayer.Character
    local Backpack = LocalPlayer.Backpack
    if not Character or not Backpack then return {} end

    local Crops = {}

    -- Check backpack
    for _, Tool in pairs(Backpack:GetChildren()) do
        local Name = Tool:FindFirstChild("Item_String")
        if Name then
            table.insert(Crops, Tool)
        end
    end

    -- Check character (equipped tools)
    for _, Tool in pairs(Character:GetChildren()) do
        local Name = Tool:FindFirstChild("Item_String")
        if Name then
            table.insert(Crops, Tool)
        end
    end

    return Crops
end

local function BuyAllSelectedSeeds()
    local selectedList = {}
    for name, sel in pairs(SelectedSeeds) do
        if sel then table.insert(selectedList, name) end
    end
    if #selectedList == 0 then return end

    for _, seedName in ipairs(selectedList) do
        local stock = SeedStock[seedName] or 0
        if stock > 0 then
            for i = 1, stock do
                BuySeed(seedName)
                task.wait(0.08)
            end
        end
    end
end

local function BuyAllSelectedGear()
    local selectedList = {}
    for name, sel in pairs(SelectedGear) do
        if sel then table.insert(selectedList, name) end
    end
    if #selectedList == 0 then return end

    for _, gearName in ipairs(selectedList) do
        local stock = GearStock[gearName] or 0
        if stock > 0 then
            for i = 1, stock do
                BuyGear(gearName)
                task.wait(0.08)
            end
        end
    end
end

local function BuyAllSelectedPetEggs()
    local selectedList = {}
    for name, sel in pairs(SelectedPetEggs) do
        if sel then table.insert(selectedList, name) end
    end
    if #selectedList == 0 then return end

    for _, eggName in ipairs(selectedList) do
        BuyPetEgg(eggName)
        task.wait(0.15) -- Slightly longer delay for pet eggs
    end
end

-- Seed reader - always scan fresh, don't keep old data
local function GetSeedStock()
    local SeedShop = PlayerGui:FindFirstChild("Seed_Shop")
    if not SeedShop then
        -- Clear old data when shop not available
        SeedStock = {}
        return {}
    end

    local sample = SeedShop:FindFirstChild("Blueberry", true)
    if not sample then
        -- Clear old data when sample not found
        SeedStock = {}
        return {}
    end

    local Items = sample.Parent

    -- Clear old data and scan fresh
    SeedStock = {}
    local NewList = {}

    -- Get fresh list of items every time
    local currentItems = Items:GetChildren()
    for _, Item in next, currentItems do
        local MainFrame = Item:FindFirstChild("Main_Frame")
        if not MainFrame then continue end
        local StockText = MainFrame:FindFirstChild("Stock_Text") and MainFrame.Stock_Text.Text or "0"
        local StockCount = tonumber(StockText:match("%d+")) or 0
        SeedStock[Item.Name] = StockCount
        NewList[Item.Name] = StockCount
    end
    return NewList
end

-- Gear reader - always scan fresh, don't keep old data
local function GetGearStock()
    local GearShop = PlayerGui:FindFirstChild("Gear_Shop")
    if not GearShop then
        -- Clear old data when shop not available
        GearStock = {}
        return {}
    end

    -- Try to find any gear item to locate the parent container
    local sample = nil
    local currentDescendants = GearShop:GetDescendants()
    for _, child in pairs(currentDescendants) do
        if child:FindFirstChild("Main_Frame") and child:FindFirstChild("Main_Frame"):FindFirstChild("Stock_Text") then
            sample = child
            break
        end
    end

    if not sample then
        -- Clear old data when sample not found
        GearStock = {}
        return {}
    end

    local Items = sample.Parent

    -- Clear old data and scan fresh
    GearStock = {}
    local NewList = {}

    -- Get fresh list of items every time
    local currentItems = Items:GetChildren()
    for _, Item in next, currentItems do
        local MainFrame = Item:FindFirstChild("Main_Frame")
        if not MainFrame then continue end
        local StockText = MainFrame:FindFirstChild("Stock_Text") and MainFrame.Stock_Text.Text or "0"
        local StockCount = tonumber(StockText:match("%d+")) or 0
        GearStock[Item.Name] = StockCount
        NewList[Item.Name] = StockCount
    end
    return NewList
end

-- Pet Egg reader - always scan fresh, don't keep old data
local function GetPetEggStock()
    local PetShopUI = PlayerGui:FindFirstChild("PetShop_UI")
    if not PetShopUI then
        -- Clear old data when shop not available
        PetEggStock = {}
        return {}
    end

    -- Clear old data and scan fresh
    PetEggStock = {}
    local NewList = {}

    -- Get fresh list of descendants every time
    local currentDescendants = PetShopUI:GetDescendants()

    -- Look for actual egg items in PetShop_UI with proper filtering
    for _, child in pairs(currentDescendants) do
        if child:IsA("Frame") or child:IsA("TextButton") then
            local eggName = nil

            -- Check if it's a proper egg item (must contain "Egg" and not be UI elements)
            if child.Name:match(".*Egg$") and not child.Name:match("Shop") and not child.Name:match("Button") and not child.Name:match("Frame") then
                eggName = child.Name
            elseif child:FindFirstChild("EggName") and child.EggName:IsA("TextLabel") then
                local eggText = child.EggName.Text
                if eggText:match(".*Egg$") and not eggText:match("Shop") then
                    eggName = eggText
                end
            elseif child:FindFirstChildOfClass("TextLabel") then
                local textLabel = child:FindFirstChildOfClass("TextLabel")
                local labelText = textLabel.Text
                -- Only accept if it ends with "Egg" and doesn't contain UI-related terms
                if labelText:match(".*Egg$") and not labelText:match("Shop") and not labelText:match("Button") and labelText ~= "Pet Eggs" then
                    eggName = labelText
                end
            end

            -- Additional filtering to exclude obvious UI elements
            if eggName and eggName ~= "" and eggName ~= "Pet Egg Shop" and not eggName:match("^Pet Egg") then
                PetEggStock[eggName] = 1
                NewList[eggName] = 1
            end
        end
    end

    -- Remove duplicates and filter out non-egg items
    local filteredList = {}
    for name, count in pairs(NewList) do
        -- Final check: must end with "Egg" and not be a UI element
        if name:match(".*Egg$") and not name:match("Shop") and name ~= "Pet Eggs" then
            filteredList[name] = count
            PetEggStock[name] = count
        end
    end

    -- Fallback: Add common egg types if none found
    if next(filteredList) == nil then
        local commonEggs = {"Common Egg", "Rare Egg", "Epic Egg", "Legendary Egg"}
        for _, eggName in ipairs(commonEggs) do
            PetEggStock[eggName] = 1
            filteredList[eggName] = 1
        end
    end

    return filteredList
end

-- Helper: remove other custom GUIs (best-effort)
local function RemoveOtherCustomGUIs(keepThisName)
    for _, child in ipairs(PlayerGui:GetChildren()) do
        if child:IsA("ScreenGui") and child.Name ~= keepThisName then
            local foundWindow = child:FindFirstChild("Window", true)
            if foundWindow then
                for _, desc in ipairs(child:GetDescendants()) do
                    if (desc:IsA("LocalScript") or desc:IsA("Script")) and desc.Parent then
                        pcall(function()
                            if desc.Disabled ~= nil then desc.Disabled = true end
                        end)
                    end
                end
                pcall(function() child:Destroy() end)
            end
        end
    end
end

RemoveOtherCustomGUIs("DepsoAutoBuyUI_temp")

--// UI setup (create UI and parent now, but hide main window until ready)
local UI = Instance.new("ScreenGui")
UI.Name = "DepsoAutoBuyUI"
UI.ResetOnSpawn = false
UI.Parent = PlayerGui
UI.ZIndexBehavior = Enum.ZIndexBehavior.Sibling

-- remove other GUIs (skip ours)
RemoveOtherCustomGUIs(UI.Name)

local function round(px)
    local cl = Instance.new("UICorner")
    cl.CornerRadius = UDim.new(0, px)
    return cl
end

local colors = {
    Window = Color3.fromRGB(30, 30, 30),
    Accent = Color3.fromRGB(70, 140, 40),
    AccentHover = Color3.fromRGB(90, 160, 60),
    Sub = Color3.fromRGB(40, 40, 40),
    Text = Color3.fromRGB(230, 230, 230),
    TabInactive = Color3.fromRGB(50, 50, 50),
}

-- Tab System Variables
local currentTab = "Shop"
local tabContents = {}

local function CreateWindow(title)
    local w = Instance.new("Frame")
    w.Name = "Window"
    w.Size = UDim2.new(0, 600, 0, 450) -- wider for three tabs
    w.Position = UDim2.new(0, 20, 0, 60)
    w.BackgroundColor3 = colors.Window
    w.BorderSizePixel = 0
    w.Parent = UI
    round(10):Clone().Parent = w

    local top = Instance.new("Frame")
    top.Name = "Top"
    top.Size = UDim2.new(1, 0, 0, 40)
    top.BackgroundColor3 = colors.Accent
    top.BorderSizePixel = 0
    top.Parent = w
    round(10):Clone().Parent = top

    local titleLbl = Instance.new("TextLabel")
    titleLbl.Size = UDim2.new(1, -84, 1, 0)
    titleLbl.Position = UDim2.new(0, 12, 0, 0)
    titleLbl.BackgroundTransparency = 1
    titleLbl.Text = title
    titleLbl.TextColor3 = colors.Text
    titleLbl.Font = Enum.Font.GothamBold
    titleLbl.TextSize = 16
    titleLbl.TextXAlignment = Enum.TextXAlignment.Left
    titleLbl.Parent = top

    local minimizeBtn = Instance.new("TextButton")
    minimizeBtn.Name = "Minimize"
    minimizeBtn.Size = UDim2.new(0, 40, 0, 26)
    minimizeBtn.Position = UDim2.new(1, -56, 0, 7)
    minimizeBtn.BackgroundColor3 = colors.Sub
    minimizeBtn.BorderSizePixel = 0
    minimizeBtn.Text = "-"
    minimizeBtn.TextColor3 = colors.Text
    minimizeBtn.Font = Enum.Font.GothamBold
    minimizeBtn.TextSize = 18
    minimizeBtn.Parent = top
    round(8):Clone().Parent = minimizeBtn

    -- Tab Container
    local tabContainer = Instance.new("Frame")
    tabContainer.Name = "TabContainer"
    tabContainer.Size = UDim2.new(1, -24, 0, 36)
    tabContainer.Position = UDim2.new(0, 12, 0, 48)
    tabContainer.BackgroundTransparency = 1
    tabContainer.Parent = w

    -- Content Container
    local content = Instance.new("Frame")
    content.Name = "Content"
    content.Size = UDim2.new(1, -24, 1, -120) -- adjusted for tabs
    content.Position = UDim2.new(0, 12, 0, 88)
    content.BackgroundColor3 = colors.Sub
    content.BorderSizePixel = 0
    content.Parent = w
    round(8):Clone().Parent = content

    return { Window = w, Top = top, TabContainer = tabContainer, Content = content, Title = titleLbl, Minimize = minimizeBtn }
end

local function CreateTab(parent, text, tabName, isActive)
    local tab = Instance.new("TextButton")
    tab.Name = tabName
    tab.Size = UDim2.new(0, 120, 1, 0)
    tab.Position = UDim2.new(0, 0, 0, 0)
    tab.BackgroundColor3 = isActive and colors.Accent or colors.TabInactive
    tab.BorderSizePixel = 0
    tab.Text = text
    tab.TextColor3 = colors.Text
    tab.Font = Enum.Font.GothamBold
    tab.TextSize = 14
    tab.Parent = parent
    round(8):Clone().Parent = tab
    
    return tab
end

local function CreateLabel(parent, text, y)
    local lbl = Instance.new("TextLabel")
    lbl.Size = UDim2.new(1, -24, 0, 20)
    lbl.Position = UDim2.new(0, 12, 0, y or 6)
    lbl.BackgroundTransparency = 1
    lbl.Text = text
    lbl.TextColor3 = colors.Text
    lbl.Font = Enum.Font.Gotham
    lbl.TextSize = 13
    lbl.TextXAlignment = Enum.TextXAlignment.Left
    lbl.Parent = parent
    return lbl
end

local function CreateButton(parent, text, y, callback)
    local btn = Instance.new("TextButton")
    btn.Size = UDim2.new(1, -24, 0, 36)
    btn.Position = UDim2.new(0, 12, 0, y)
    btn.BackgroundColor3 = colors.Accent
    btn.Text = text
    btn.TextColor3 = Color3.new(1,1,1)
    btn.Font = Enum.Font.GothamBold
    btn.TextSize = 14
    btn.BorderSizePixel = 0
    btn.Parent = parent
    round(8):Clone().Parent = btn
    btn.MouseButton1Click:Connect(function() pcall(callback) end)
    return btn
end

local function CreateCheckbox(parent, label, initial, y)
    local container = Instance.new("Frame")
    container.Size = UDim2.new(1, -24, 0, 30)
    container.Position = UDim2.new(0, 12, 0, y)
    container.BackgroundTransparency = 1
    container.Parent = parent

    local box = Instance.new("TextButton")
    box.Size = UDim2.new(0, 24, 0, 24)
    box.Position = UDim2.new(0, 6, 0, 3)
    box.BackgroundColor3 = colors.Window
    box.BorderSizePixel = 0
    box.Text = ""
    box.Parent = container
    round(6):Clone().Parent = box

    local lbl = Instance.new("TextLabel")
    lbl.Size = UDim2.new(1, -48, 1, 0)
    lbl.Position = UDim2.new(0, 40, 0, 0)
    lbl.BackgroundTransparency = 1
    lbl.Text = label
    lbl.TextColor3 = colors.Text
    lbl.Font = Enum.Font.Gotham
    lbl.TextSize = 13
    lbl.TextXAlignment = Enum.TextXAlignment.Left
    lbl.Parent = container

    local checked = Instance.new("Frame")
    checked.Size = UDim2.new(1, -6, 1, -6)
    checked.Position = UDim2.new(0, 3, 0, 3)
    checked.BackgroundColor3 = colors.Accent
    checked.Visible = initial
    checked.Parent = box
    round(4):Clone().Parent = checked

    local state = { Value = initial }
    box.MouseButton1Click:Connect(function()
        state.Value = not state.Value
        checked.Visible = state.Value
    end)

    return state, container
end

-- CreateCombo with improved selection reliability
local function CreateCombo(parent, label, getItems, selectedTable, y)
    local container = Instance.new("Frame")
    container.Size = UDim2.new(1, -24, 0, 80)
    container.Position = UDim2.new(0, 12, 0, y)
    container.BackgroundTransparency = 1
    container.Parent = parent

    local lbl = Instance.new("TextLabel")
    lbl.Size = UDim2.new(1, 0, 0, 18)
    lbl.Position = UDim2.new(0, 0, 0, 0)
    lbl.BackgroundTransparency = 1
    lbl.Text = label
    lbl.TextColor3 = colors.Text
    lbl.Font = Enum.Font.Gotham
    lbl.TextSize = 13
    lbl.TextXAlignment = Enum.TextXAlignment.Left
    lbl.Parent = container

    local box = Instance.new("TextButton")
    box.Size = UDim2.new(1, 0, 0, 36)
    box.Position = UDim2.new(0, 0, 0, 22)
    box.BackgroundColor3 = colors.Window
    box.BorderSizePixel = 0
    box.Text = ""
    box.Active = true
    box.Parent = container
    round(8):Clone().Parent = box

    local selectedLabel = Instance.new("TextLabel")
    selectedLabel.Size = UDim2.new(1, -12, 1, 0)
    selectedLabel.Position = UDim2.new(0, 8, 0, 0)
    selectedLabel.BackgroundTransparency = 1
    selectedLabel.Text = "(select)"
    selectedLabel.TextColor3 = colors.Text
    selectedLabel.Font = Enum.Font.Gotham
    selectedLabel.TextSize = 13
    selectedLabel.TextXAlignment = Enum.TextXAlignment.Left
    selectedLabel.Parent = box

    local dropdown = Instance.new("Frame")
    dropdown.Name = "ComboDropdown"
    dropdown.Size = UDim2.fromOffset(260, 120)
    dropdown.Position = UDim2.fromOffset(0, 0)
    dropdown.BackgroundColor3 = colors.Sub
    dropdown.BorderSizePixel = 0
    dropdown.Visible = false
    dropdown.ZIndex = 50
    dropdown.Parent = UI
    round(8):Clone().Parent = dropdown

    local shadow = Instance.new("Frame")
    shadow.Size = UDim2.new(1, 6, 1, 6)
    shadow.Position = UDim2.new(0, -3, 0, -3)
    shadow.BackgroundTransparency = 0.88
    shadow.BackgroundColor3 = Color3.new(0,0,0)
    shadow.BorderSizePixel = 0
    shadow.Parent = dropdown
    round(10):Clone().Parent = shadow
    shadow.ZIndex = 49

    local scroll = Instance.new("ScrollingFrame")
    scroll.Size = UDim2.new(1, -8, 1, -8)
    scroll.Position = UDim2.new(0, 4, 0, 4)
    scroll.BackgroundTransparency = 1
    scroll.BorderSizePixel = 0
    scroll.Parent = dropdown
    scroll.ScrollBarThickness = 8
    scroll.AutomaticCanvasSize = Enum.AutomaticSize.Y
    scroll.ZIndex = 51

    local uiList = Instance.new("UIListLayout")
    uiList.Parent = scroll
    uiList.Padding = UDim.new(0, 6)
    uiList.SortOrder = Enum.SortOrder.LayoutOrder

    local open = false
    local isPopulating = false
    local lastPopulate = 0

    local function clampDropdownToScreen(px, py, w, h)
        local cam = workspace.CurrentCamera
        local screenW = cam and cam.ViewportSize.X or 800
        local screenH = cam and cam.ViewportSize.Y or 600
        if px + w > screenW then px = math.max(4, screenW - w - 4) end
        if py + h > screenH then py = math.max(4, screenH - h - 4) end
        return px, py
    end

    local function updateSelectedLabel()
        local names = {}
        for name, sel in pairs(selectedTable) do
            if sel then table.insert(names, name) end
        end
        if #names == 0 then
            selectedLabel.Text = "(select)"
        elseif #names <= 3 then
            selectedLabel.Text = table.concat(names, ", ")
        else
            selectedLabel.Text = names[1] .. ", " .. names[2] .. " +" .. tostring(#names - 2)
        end
    end

    local function populate()
        if isPopulating then return end
        isPopulating = true
        lastPopulate = tick()

        for _, child in next, scroll:GetChildren() do
            if child:IsA("Frame") or child:IsA("TextButton") then child:Destroy() end
        end

        local items = {}
        pcall(function() items = getItems() end)

        local count = 0
        for name, _ in pairs(items) do
            count = count + 1
            local row = Instance.new("Frame")
            row.Size = UDim2.new(1, -8, 0, 30)
            row.LayoutOrder = count
            row.BackgroundTransparency = 1
            row.Parent = scroll

            local it = Instance.new("TextButton")
            it.Size = UDim2.new(1, 0, 1, 0)
            it.Position = UDim2.new(0, 0, 0, 0)
            it.BackgroundColor3 = colors.Window
            it.BorderSizePixel = 0
            it.AutoButtonColor = true
            it.Text = ""
            it.Active = true
            it.Selectable = true
            it.Parent = row
            round(6):Clone().Parent = it
            it.ZIndex = 52

            local nameLbl = Instance.new("TextLabel")
            nameLbl.Size = UDim2.new(1, -40, 1, 0)
            nameLbl.Position = UDim2.new(0, 8, 0, 0)
            nameLbl.BackgroundTransparency = 1
            nameLbl.Text = name
            nameLbl.TextColor3 = colors.Text
            nameLbl.Font = Enum.Font.Gotham
            nameLbl.TextSize = 13
            nameLbl.TextXAlignment = Enum.TextXAlignment.Left
            nameLbl.Parent = it
            nameLbl.ZIndex = 53

            local checkBox = Instance.new("Frame")
            checkBox.Size = UDim2.new(0, 24, 0, 24)
            checkBox.Position = UDim2.new(1, -34, 0, 3)
            checkBox.BackgroundColor3 = colors.Window
            checkBox.BorderSizePixel = 0
            checkBox.Parent = it
            round(6):Clone().Parent = checkBox
            checkBox.ZIndex = 53

            local tickFrame = Instance.new("Frame")
            tickFrame.Size = UDim2.new(1, -6, 1, -6)
            tickFrame.Position = UDim2.new(0, 3, 0, 3)
            tickFrame.BackgroundColor3 = colors.Accent
            tickFrame.Visible = selectedTable[name] == true
            tickFrame.Parent = checkBox
            round(4):Clone().Parent = tickFrame
            tickFrame.ZIndex = 54

            -- hover visuals
            it.MouseEnter:Connect(function() it.BackgroundColor3 = Color3.fromRGB(60,60,60) end)
            it.MouseLeave:Connect(function() it.BackgroundColor3 = colors.Window end)

            -- toggle selection with small debounce
            local lastToggle = 0
            it.MouseButton1Click:Connect(function()
                local now = tick()
                if now - lastToggle < 0.08 then return end
                lastToggle = now
                selectedTable[name] = not selectedTable[name]
                tickFrame.Visible = selectedTable[name] == true
                updateSelectedLabel()
            end)
        end

        task.spawn(function()
            task.wait()
            local contentHeight = uiList.AbsoluteContentSize.Y
            local desiredW = math.max(box.AbsoluteSize.X, 240)
            local desiredH = math.min(contentHeight + 12, 300)
            local boxAbsPos = box.AbsolutePosition
            local boxSize = box.AbsoluteSize
            local px, py = clampDropdownToScreen(boxAbsPos.X, boxAbsPos.Y + boxSize.Y + 6, desiredW, desiredH)
            dropdown.Position = UDim2.fromOffset(px, py)
            dropdown.Size = UDim2.fromOffset(desiredW, desiredH)
            isPopulating = false
        end)
    end

    box.MouseButton1Click:Connect(function()
        open = not open
        dropdown.Visible = open
        if open then
            populate()
        end
    end)

    UserInputService.InputBegan:Connect(function(input, gameProcessed)
        if not open then return end
        if input.UserInputType ~= Enum.UserInputType.MouseButton1 and input.UserInputType ~= Enum.UserInputType.Touch then return end
        local pos = input.Position
        local mx, my = pos.X, pos.Y
        local absPos = dropdown.AbsolutePosition
        local absSize = dropdown.AbsoluteSize
        local bxPos = box.AbsolutePosition
        local bxSize = box.AbsoluteSize
        local inDropdown = (mx >= absPos.X and mx <= absPos.X + absSize.X and my >= absPos.Y and my <= absPos.Y + absSize.Y)
        local inBox = (mx >= bxPos.X and mx <= bxPos.X + bxSize.X and my >= bxPos.Y and my <= bxPos.Y + bxSize.Y)
        if not (inDropdown or inBox) then
            dropdown.Visible = false
            open = false
        end
    end)

    return { Container = container, Populate = populate, Dropdown = dropdown, Box = box, UpdateLabel = updateSelectedLabel, IsPopulating = function() return isPopulating end, LastPopulate = function() return lastPopulate end }
end

-- Build window (but keep hidden until ready)
local win = CreateWindow(GameInfo.Name .. " | Depso")
win.Window.Visible = false -- HIDE main GUI until ready

-- Create Tabs
local shopTab = CreateTab(win.TabContainer, "Shop", "Shop", true)
shopTab.Position = UDim2.new(0, 0, 0, 0)

local miscTab = CreateTab(win.TabContainer, "Miscellaneous", "Miscellaneous", false)
miscTab.Position = UDim2.new(0, 130, 0, 0)

local autoFarmTab = CreateTab(win.TabContainer, "Auto Farm", "AutoFarm", false)
autoFarmTab.Position = UDim2.new(0, 260, 0, 0)

-- Create tab contents
local function createShopContent(parent)
    local content = Instance.new("Frame")
    content.Name = "ShopContent"
    content.Size = UDim2.new(1, 0, 1, 0)
    content.BackgroundTransparency = 1
    content.Parent = parent

    -- Seeds Section (Left side)
    local seedCombo = CreateCombo(content, "Seeds", function() return GetSeedStock() end, SelectedSeeds, 8)
    local seedAutoState, seedAutoFrame = CreateCheckbox(content, "Auto-Buy Seeds Enabled", false, 95)
    
    -- Pet Eggs Section (Left side, below seeds)
    local petEggCombo = CreateCombo(content, "Pet Eggs", function() return GetPetEggStock() end, SelectedPetEggs, 135)
    local petEggAutoState, petEggAutoFrame = CreateCheckbox(content, "Auto-Buy Pet Eggs Enabled", false, 222)

    -- Gear Section (Right side)
    -- Create gear content container on the right
    local gearContainer = Instance.new("Frame")
    gearContainer.Size = UDim2.new(0.5, -12, 1, 0)
    gearContainer.Position = UDim2.new(0.5, 12, 0, 0)
    gearContainer.BackgroundTransparency = 1
    gearContainer.Parent = content

    local gearCombo = CreateCombo(gearContainer, "Gear", function() return GetGearStock() end, SelectedGear, 8)
    local gearAutoState, gearAutoFrame = CreateCheckbox(gearContainer, "Auto-Buy Gear Enabled", false, 95)

    -- Adjust seeds section to left side only
    seedCombo.Container.Size = UDim2.new(0.5, -12, 0, 80)
    seedAutoFrame.Size = UDim2.new(0.5, -12, 0, 30)
    
    -- Adjust pet eggs section to left side only
    petEggCombo.Container.Size = UDim2.new(0.5, -12, 0, 80)
    petEggAutoFrame.Size = UDim2.new(0.5, -12, 0, 30)

    return { 
        Content = content, 
        SeedCombo = seedCombo, 
        GearCombo = gearCombo, 
        PetEggCombo = petEggCombo,
        SeedAutoState = seedAutoState, 
        GearAutoState = gearAutoState,
        PetEggAutoState = petEggAutoState
    }
end

local function createMiscContent(parent)
    local content = Instance.new("Frame")
    content.Name = "MiscContent"
    content.Size = UDim2.new(1, 0, 1, 0)
    content.BackgroundTransparency = 1
    content.Visible = false
    content.Parent = parent

    -- Sell Inventory Button
    local sellBtn = CreateButton(content, "Sell Inventory", 20, function()
        SellInventory()
    end)

    -- Auto Sell Toggle
    local autoSellState, autoSellFrame = CreateCheckbox(content, "Auto Sell Enabled", false, 70)

    -- Sell Threshold Label
    local thresholdLabel = CreateLabel(content, "Sell when inventory has 15+ crops", 110)

    -- Infinite Sprinkler Button
    local sprinklerBtn = CreateButton(content, "Infinite Sprinkler", 150, function()
        -- Simple infinite sprinkler - just delete sprinklers to reset effect
        local ObjectsFolder = workspace.Farm.Farm.Important.Objects_Physical
        local DeleteRemote = ReplicatedStorage:WaitForChild("GameEvents"):WaitForChild("DeleteObject")

        -- Find and delete all sprinklers
        for _, obj in ipairs(ObjectsFolder:GetChildren()) do
            if obj:IsA("Model") and obj.Name:lower():find("basic") then
                DeleteRemote:FireServer(obj)
            end
        end
    end)

    return {
        Content = content,
        AutoSellState = autoSellState,
        ThresholdLabel = thresholdLabel
    }
end

-- Auto Farm System Variables
local AutoFarmSystem = {
    isActive = false,
    connections = {},
    lastScanTime = 0
}

-- Auto Plant System Variables
local AutoPlantSystem = {
    isActive = false,
    selectedSeed = "",
    connections = {},
    lastPlantTime = 0,
    plantThrottle = 0.3 -- Minimum time between plants (300ms)
}

-- Ultra-fast harvest function for instant collection
local function HarvestPlant(Plant)
    if not Plant then return false end

    local Prompt = Plant:FindFirstChild("ProximityPrompt", true)

    -- Minimal checks for maximum speed
    if not Prompt or not Prompt.Enabled then return false end

    -- Instant harvest without delays
    pcall(function()
        fireproximityprompt(Prompt)
    end)

    return true -- Assume success for speed
end

-- Check if a plant can be harvested (from reference script)
local function CanHarvest(Plant)
    local Prompt = Plant:FindFirstChild("ProximityPrompt", true)
    if not Prompt then return false end
    if not Prompt.Enabled then return false end
    return true
end

-- Collect harvestable plants from a parent (from reference script)
local function CollectHarvestable(Parent, Plants, IgnoreDistance)
    local Character = LocalPlayer.Character
    if not Character then return Plants end

    local PlayerPosition = Character:GetPivot().Position

    for _, Plant in pairs(Parent:GetChildren()) do
        -- Check for Fruits subfolder (some plants have fruits in subfolders)
        local Fruits = Plant:FindFirstChild("Fruits")
        if Fruits then
            CollectHarvestable(Fruits, Plants, IgnoreDistance)
        end

        -- Distance check (only if not ignoring distance)
        if not IgnoreDistance then
            local PlantPosition = Plant:GetPivot().Position
            local Distance = (PlayerPosition - PlantPosition).Magnitude
            if Distance > 50 then continue end -- Increased range for better collection
        end

        -- Check if plant can be harvested
        if CanHarvest(Plant) then
            table.insert(Plants, Plant)
        end
    end

    return Plants
end

-- Get all harvestable plants (from reference script)
local function GetHarvestablePlants(IgnoreDistance)
    local Plants = {}

    -- Get player's farm
    local MyFarm = nil
    local farmFolder = workspace:FindFirstChild("Farm")
    if farmFolder then
        for _, farm in pairs(farmFolder:GetChildren()) do
            local Important = farm:FindFirstChild("Important")
            if Important then
                local Data = Important:FindFirstChild("Data")
                if Data then
                    local Owner = Data:FindFirstChild("Owner")
                    if Owner and Owner.Value == LocalPlayer.Name then
                        MyFarm = farm
                        break
                    end
                end
            end
        end
    end

    if MyFarm then
        local Important = MyFarm:FindFirstChild("Important")
        if Important then
            local PlantsPhysical = Important:FindFirstChild("Plants_Physical")
            if PlantsPhysical then
                CollectHarvestable(PlantsPhysical, Plants, IgnoreDistance)
            end
        end
    end

    return Plants
end

-- Auto Plant System Functions
local Backpack = LocalPlayer.Backpack

-- EquipCheck function from the reference
local function EquipCheck(Tool)
    local Character = LocalPlayer.Character
    local Humanoid = Character.Humanoid

    if Tool.Parent ~= Backpack then return end
    Humanoid:EquipTool(Tool)
end

-- Function to get available seeds from backpack
local function GetAvailableSeeds()
    local availableSeeds = {}
    local Character = LocalPlayer.Character

    -- Check backpack
    for _, tool in pairs(Backpack:GetChildren()) do
        if tool:IsA("Tool") and tool.Name:find("Seed") then
            availableSeeds[tool.Name] = tool
        end
    end

    -- Check character (equipped tools)
    if Character then
        for _, tool in pairs(Character:GetChildren()) do
            if tool:IsA("Tool") and tool.Name:find("Seed") then
                availableSeeds[tool.Name] = tool
            end
        end
    end

    return availableSeeds
end

-- Function to check if selected seed is available in inventory
local function CheckSeedInventory(seedName)
    local availableSeeds = GetAvailableSeeds()

    -- Check for exact match first
    if availableSeeds[seedName] then
        return availableSeeds[seedName]
    end

    -- Check for seeds that contain the selected name and "Seed"
    for toolName, tool in pairs(availableSeeds) do
        if toolName:find(seedName) and toolName:find("Seed") then
            return tool
        end
    end

    return nil
end

-- Plant function adapted from reference
local function Plant(Position, Seed)
    pcall(function()
        GameEvents.Plant_RE:FireServer(Position, Seed)
    end)
    task.wait(0.3)
end

-- Get farm area functions adapted from reference
local function GetArea(Base)
    local Center = Base:GetPivot()
    local Size = Base.Size

    -- Bottom left
    local X1 = math.ceil(Center.X - (Size.X/2))
    local Z1 = math.ceil(Center.Z - (Size.Z/2))

    -- Top right
    local X2 = math.floor(Center.X + (Size.X/2))
    local Z2 = math.floor(Center.Z + (Size.Z/2))

    return X1, Z1, X2, Z2
end

local function GetFarm(PlayerName)
    local Farms = workspace.Farm:GetChildren()
    for _, Farm in pairs(Farms) do
        local Important = Farm:FindFirstChild("Important")
        if Important then
            local Data = Important:FindFirstChild("Data")
            if Data then
                local Owner = Data:FindFirstChild("Owner")
                if Owner and Owner.Value == PlayerName then
                    return Farm
                end
            end
        end
    end
    return nil
end

local function GetRandomFarmPoint()
    local MyFarm = GetFarm(LocalPlayer.Name)
    if not MyFarm then return nil end

    local Important = MyFarm:FindFirstChild("Important")
    if not Important then return nil end

    local PlantLocations = Important:FindFirstChild("Plant_Locations")
    if not PlantLocations then return nil end

    local FarmLands = PlantLocations:GetChildren()
    if #FarmLands == 0 then return nil end

    local FarmLand = FarmLands[math.random(1, #FarmLands)]
    local X1, Z1, X2, Z2 = GetArea(FarmLand)
    local X = math.random(X1, X2)
    local Z = math.random(Z1, Z2)

    return Vector3.new(X, 4, Z)
end

-- Auto plant function
local function AutoPlantLoop()
    if not AutoPlantSystem.isActive then return end
    if AutoPlantSystem.selectedSeed == "" then return end

    local currentTime = tick()
    if currentTime - AutoPlantSystem.lastPlantTime < AutoPlantSystem.plantThrottle then
        return
    end

    -- Check if seed is available in inventory
    local seedTool = CheckSeedInventory(AutoPlantSystem.selectedSeed)
    if not seedTool then return end

    -- Equip the seed tool
    EquipCheck(seedTool)

    -- Get a random farm point
    local plantPosition = GetRandomFarmPoint()
    if not plantPosition then return end

    -- Plant the seed
    Plant(plantPosition, AutoPlantSystem.selectedSeed)
    AutoPlantSystem.lastPlantTime = currentTime
end



-- Function to get seed names for dropdown (from shop data)
local function GetSeedNamesForDropdown()
    local seedNames = {}
    local seedStock = GetSeedStock()

    for seedName, _ in pairs(seedStock) do
        seedNames[seedName] = 1 -- Just need the name, value doesn't matter for dropdown
    end

    return seedNames
end

-- Create dropdown for auto plant system
local function CreateDropdown(parent, label, getItems, y)
    local container = Instance.new("Frame")
    container.Size = UDim2.new(1, -24, 0, 80)
    container.Position = UDim2.new(0, 12, 0, y)
    container.BackgroundTransparency = 1
    container.Parent = parent

    local lbl = Instance.new("TextLabel")
    lbl.Size = UDim2.new(1, 0, 0, 18)
    lbl.Position = UDim2.new(0, 0, 0, 0)
    lbl.BackgroundTransparency = 1
    lbl.Text = label
    lbl.TextColor3 = colors.Text
    lbl.Font = Enum.Font.Gotham
    lbl.TextSize = 13
    lbl.TextXAlignment = Enum.TextXAlignment.Left
    lbl.Parent = container

    local box = Instance.new("TextButton")
    box.Size = UDim2.new(1, 0, 0, 36)
    box.Position = UDim2.new(0, 0, 0, 22)
    box.BackgroundColor3 = colors.Window
    box.BorderSizePixel = 0
    box.Text = ""
    box.Active = true
    box.Parent = container
    round(8):Clone().Parent = box

    local selectedLabel = Instance.new("TextLabel")
    selectedLabel.Size = UDim2.new(1, -12, 1, 0)
    selectedLabel.Position = UDim2.new(0, 8, 0, 0)
    selectedLabel.BackgroundTransparency = 1
    selectedLabel.Text = "(select seed)"
    selectedLabel.TextColor3 = colors.Text
    selectedLabel.Font = Enum.Font.Gotham
    selectedLabel.TextSize = 13
    selectedLabel.TextXAlignment = Enum.TextXAlignment.Left
    selectedLabel.Parent = box

    local dropdown = Instance.new("Frame")
    dropdown.Name = "SeedDropdown"
    dropdown.Size = UDim2.fromOffset(260, 120)
    dropdown.Position = UDim2.fromOffset(0, 0)
    dropdown.BackgroundColor3 = colors.Sub
    dropdown.BorderSizePixel = 0
    dropdown.Visible = false
    dropdown.ZIndex = 50
    dropdown.Parent = UI
    round(8):Clone().Parent = dropdown

    local shadow = Instance.new("Frame")
    shadow.Size = UDim2.new(1, 6, 1, 6)
    shadow.Position = UDim2.new(0, -3, 0, -3)
    shadow.BackgroundTransparency = 0.88
    shadow.BackgroundColor3 = Color3.new(0,0,0)
    shadow.BorderSizePixel = 0
    shadow.Parent = dropdown
    round(10):Clone().Parent = shadow
    shadow.ZIndex = 49

    local scroll = Instance.new("ScrollingFrame")
    scroll.Size = UDim2.new(1, -8, 1, -8)
    scroll.Position = UDim2.new(0, 4, 0, 4)
    scroll.BackgroundTransparency = 1
    scroll.BorderSizePixel = 0
    scroll.Parent = dropdown
    scroll.ScrollBarThickness = 8
    scroll.AutomaticCanvasSize = Enum.AutomaticSize.Y
    scroll.ZIndex = 51

    local uiList = Instance.new("UIListLayout")
    uiList.Parent = scroll
    uiList.Padding = UDim.new(0, 6)
    uiList.SortOrder = Enum.SortOrder.LayoutOrder

    local open = false
    local selectedSeed = ""

    local function clampDropdownToScreen(px, py, w, h)
        local cam = workspace.CurrentCamera
        local screenW = cam and cam.ViewportSize.X or 800
        local screenH = cam and cam.ViewportSize.Y or 600
        if px + w > screenW then px = math.max(4, screenW - w - 4) end
        if py + h > screenH then py = math.max(4, screenH - h - 4) end
        return px, py
    end

    local function populate()
        for _, child in pairs(scroll:GetChildren()) do
            if child:IsA("Frame") or child:IsA("TextButton") then child:Destroy() end
        end

        local items = {}
        pcall(function() items = getItems() end)

        local count = 0
        for name, _ in pairs(items) do
            count = count + 1
            local row = Instance.new("Frame")
            row.Size = UDim2.new(1, -8, 0, 30)
            row.LayoutOrder = count
            row.BackgroundTransparency = 1
            row.Parent = scroll

            local it = Instance.new("TextButton")
            it.Size = UDim2.new(1, 0, 1, 0)
            it.Position = UDim2.new(0, 0, 0, 0)
            it.BackgroundColor3 = colors.Window
            it.BorderSizePixel = 0
            it.AutoButtonColor = true
            it.Text = ""
            it.Active = true
            it.Selectable = true
            it.Parent = row
            round(6):Clone().Parent = it
            it.ZIndex = 52

            local nameLbl = Instance.new("TextLabel")
            nameLbl.Size = UDim2.new(1, -8, 1, 0)
            nameLbl.Position = UDim2.new(0, 8, 0, 0)
            nameLbl.BackgroundTransparency = 1
            nameLbl.Text = name
            nameLbl.TextColor3 = colors.Text
            nameLbl.Font = Enum.Font.Gotham
            nameLbl.TextSize = 13
            nameLbl.TextXAlignment = Enum.TextXAlignment.Left
            nameLbl.Parent = it
            nameLbl.ZIndex = 53

            -- hover visuals
            it.MouseEnter:Connect(function() it.BackgroundColor3 = Color3.fromRGB(60,60,60) end)
            it.MouseLeave:Connect(function() it.BackgroundColor3 = colors.Window end)

            -- selection
            it.MouseButton1Click:Connect(function()
                selectedSeed = name
                selectedLabel.Text = name
                AutoPlantSystem.selectedSeed = name
                dropdown.Visible = false
                open = false
            end)
        end

        task.spawn(function()
            task.wait()
            local contentHeight = uiList.AbsoluteContentSize.Y
            local desiredW = math.max(box.AbsoluteSize.X, 240)
            local desiredH = math.min(contentHeight + 12, 300)
            local boxAbsPos = box.AbsolutePosition
            local boxSize = box.AbsoluteSize
            local px, py = clampDropdownToScreen(boxAbsPos.X, boxAbsPos.Y + boxSize.Y + 6, desiredW, desiredH)
            dropdown.Position = UDim2.fromOffset(px, py)
            dropdown.Size = UDim2.fromOffset(desiredW, desiredH)
        end)
    end

    box.MouseButton1Click:Connect(function()
        open = not open
        dropdown.Visible = open
        if open then
            populate()
        end
    end)

    UserInputService.InputBegan:Connect(function(input, gameProcessed)
        if not open then return end
        if input.UserInputType ~= Enum.UserInputType.MouseButton1 and input.UserInputType ~= Enum.UserInputType.Touch then return end
        local pos = input.Position
        local mx, my = pos.X, pos.Y
        local absPos = dropdown.AbsolutePosition
        local absSize = dropdown.AbsoluteSize
        local bxPos = box.AbsolutePosition
        local bxSize = box.AbsoluteSize
        local inDropdown = (mx >= absPos.X and mx <= absPos.X + absSize.X and my >= absPos.Y and my <= absPos.Y + absSize.Y)
        local inBox = (mx >= bxPos.X and mx <= bxPos.X + bxSize.X and my >= bxPos.Y and my <= bxPos.Y + bxSize.Y)
        if not (inDropdown or inBox) then
            dropdown.Visible = false
            open = false
        end
    end)

    return { Container = container, Dropdown = dropdown, Box = box, Populate = populate, GetSelected = function() return selectedSeed end }
end

local function createAutoFarmContent(parent)
    local content = Instance.new("Frame")
    content.Name = "AutoFarmContent"
    content.Size = UDim2.new(1, 0, 1, 0)
    content.BackgroundTransparency = 1
    content.Visible = false
    content.Parent = parent

    -- Auto Collect Toggle
    local autoFarmState, autoFarmFrame = CreateCheckbox(content, "Auto Collect", false, 20)

    -- Status Labels
    local statusLabel = CreateLabel(content, "Status: Stopped", 60)

    -- Auto Plant Section
    local autoPlantLabel = CreateLabel(content, "Auto Plant System:", 100)
    autoPlantLabel.Font = Enum.Font.GothamBold
    autoPlantLabel.TextSize = 14

    -- Seed Selection Dropdown
    local seedDropdown = CreateDropdown(content, "Select Seed:", GetSeedNamesForDropdown, 125)

    -- Auto Plant Toggle
    local autoPlantState, autoPlantFrame = CreateCheckbox(content, "Auto Plant Enabled", false, 215)

    -- Auto Plant Status
    local plantStatusLabel = CreateLabel(content, "Plant Status: Stopped", 255)

    -- Auto Collect Control Logic
    local function updateAutoFarmStatus()
        if AutoFarmSystem.isActive then
            local totalHarvests = AutoFarmHeartbeat.performanceStats.totalHarvests
            statusLabel.Text = "Status:  " .. totalHarvests .. ")"
        else
            statusLabel.Text = "Status: Stopped"
        end
    end

    local function updateAutoPlantStatus()
        if AutoPlantSystem.isActive then
            if AutoPlantSystem.selectedSeed ~= "" then
                plantStatusLabel.Text = "Plant Status: Planting " .. AutoPlantSystem.selectedSeed
            else
                plantStatusLabel.Text = "Plant Status: No seed selected"
            end
        else
            plantStatusLabel.Text = "Plant Status: Stopped"
        end
    end

    -- Toggle auto farm when checkbox is clicked
    autoFarmState.Value = false
    local lastToggle = 0

    -- Monitor checkbox state changes
    task.spawn(function()
        while true do
            task.wait(0.1)
            if autoFarmState.Value ~= AutoFarmSystem.isActive then
                local now = tick()
                if now - lastToggle > 0.2 then -- Debounce
                    lastToggle = now
                    AutoFarmSystem.isActive = autoFarmState.Value

                    if AutoFarmSystem.isActive then
                        AutoFarmSystem.lastScanTime = 0
                    end
                end
            end

            -- Monitor auto plant state changes
            if autoPlantState.Value ~= AutoPlantSystem.isActive then
                local now = tick()
                if now - lastToggle > 0.2 then -- Debounce
                    lastToggle = now
                    AutoPlantSystem.isActive = autoPlantState.Value
                end
            end

            updateAutoFarmStatus()
            updateAutoPlantStatus()
        end
    end)

    return {
        Content = content,
        AutoFarmState = autoFarmState,
        StatusLabel = statusLabel,
        AutoPlantState = autoPlantState,
        PlantStatusLabel = plantStatusLabel,
        SeedDropdown = seedDropdown
    }
end

-- Create tab contents
tabContents.Shop = createShopContent(win.Content)
tabContents.Miscellaneous = createMiscContent(win.Content)
tabContents.AutoFarm = createAutoFarmContent(win.Content)

-- Tab switching logic
local function switchTab(tabName)
    if currentTab == tabName then return end

    -- Update tab buttons
    for _, tab in pairs({shopTab, miscTab, autoFarmTab}) do
        if tab.Name == tabName then
            tab.BackgroundColor3 = colors.Accent
        else
            tab.BackgroundColor3 = colors.TabInactive
        end
    end

    -- Update content visibility
    for name, tabContent in pairs(tabContents) do
        tabContent.Content.Visible = (name == tabName)
    end

    currentTab = tabName
end

-- Connect tab buttons
shopTab.MouseButton1Click:Connect(function() switchTab("Shop") end)
miscTab.MouseButton1Click:Connect(function() switchTab("Miscellaneous") end)
autoFarmTab.MouseButton1Click:Connect(function() switchTab("AutoFarm") end)

-- drag & minimize
local dragging = false
local dragStart, startPos, dragConn
local isMinimized = false
local prevSize = win.Window.Size
local prevContentVisible = true

local function updateDrag(input)
    if not dragging or not dragStart or not startPos then return end
    local delta = input.Position - dragStart
    local newX = startPos.X.Offset + delta.X
    local newY = startPos.Y.Offset + delta.Y
    win.Window.Position = UDim2.new(0, newX, 0, newY)
end

win.Top.InputBegan:Connect(function(input)
    if input.UserInputType == Enum.UserInputType.MouseButton1 or input.UserInputType == Enum.UserInputType.Touch then
        dragging = true
        dragStart = input.Position
        startPos = win.Window.Position
        dragConn = input.Changed:Connect(function() if input.UserInputState == Enum.UserInputState.End then dragging = false end end)
    end
end)

UserInputService.InputChanged:Connect(function(input)
    if input.UserInputType == Enum.UserInputType.MouseMovement or input.UserInputType == Enum.UserInputType.Touch then
        if dragging then pcall(updateDrag, input) end
    end
end)

UserInputService.InputEnded:Connect(function(input)
    if dragConn and input.UserInputType == Enum.UserInputType.MouseButton1 then dragConn:Disconnect(); dragConn = nil end
end)

win.Minimize.MouseButton1Click:Connect(function()
    isMinimized = not isMinimized
    if isMinimized then
        prevSize = win.Window.Size
        prevContentVisible = win.Content.Visible
        TweenService:Create(win.Window, TweenInfo.new(0.18), { Size = UDim2.new(win.Window.Size.X.Scale, win.Window.Size.X.Offset, 0, win.Top.Size.Y.Offset + 6) }):Play()
        win.Content.Visible = false
        win.TabContainer.Visible = false
        win.Minimize.Text = "+"
    else
        TweenService:Create(win.Window, TweenInfo.new(0.18), { Size = prevSize }):Play()
        win.Content.Visible = prevContentVisible
        win.TabContainer.Visible = true
        win.Minimize.Text = "-"
    end
end)

-- hide combo dropdowns if window moves
win.Window:GetPropertyChangedSignal("Position"):Connect(function()
    local shopContent = tabContents.Shop
    if shopContent then
        if shopContent.SeedCombo and shopContent.SeedCombo.Dropdown and shopContent.SeedCombo.Dropdown.Visible then
            shopContent.SeedCombo.Dropdown.Visible = false
        end
        if shopContent.GearCombo and shopContent.GearCombo.Dropdown and shopContent.GearCombo.Dropdown.Visible then
            shopContent.GearCombo.Dropdown.Visible = false
        end
        if shopContent.PetEggCombo and shopContent.PetEggCombo.Dropdown and shopContent.PetEggCombo.Dropdown.Visible then
            shopContent.PetEggCombo.Dropdown.Visible = false
        end
    end

    -- Hide auto farm seed dropdown too
    local autoFarmContent = tabContents.AutoFarm
    if autoFarmContent and autoFarmContent.SeedDropdown and autoFarmContent.SeedDropdown.Dropdown and autoFarmContent.SeedDropdown.Dropdown.Visible then
        autoFarmContent.SeedDropdown.Dropdown.Visible = false
    end
end)

-- initial populate and wait for completion; only show main GUI after both fakeDone and populate finished
local function waitForInitialPopulateAndFake()
    -- Populate shop combos
    local shopContent = tabContents.Shop
    if shopContent then
        if shopContent.SeedCombo and shopContent.SeedCombo.Populate then
            shopContent.SeedCombo.Populate()
        end
        if shopContent.GearCombo and shopContent.GearCombo.Populate then
            shopContent.GearCombo.Populate()
        end
        if shopContent.PetEggCombo and shopContent.PetEggCombo.Populate then
            shopContent.PetEggCombo.Populate()
        end
    end
    
    -- Wait for populate finish or timeout
    local t0 = tick()
    local timeout = 8
    while tick() - t0 < timeout do
        local allDone = true
        if shopContent then
            if shopContent.SeedCombo and shopContent.SeedCombo.IsPopulating and shopContent.SeedCombo.IsPopulating() then
                allDone = false
            end
            if shopContent.GearCombo and shopContent.GearCombo.IsPopulating and shopContent.GearCombo.IsPopulating() then
                allDone = false
            end
            if shopContent.PetEggCombo and shopContent.PetEggCombo.IsPopulating and shopContent.PetEggCombo.IsPopulating() then
                allDone = false
            end
        end
        if allDone then break end
        task.wait(0.05)
    end
    
    -- Also wait until fake timer done
    local tstart = tick()
    while not fakeDone and (tick() - tstart) < (FAKE_LOADING_SECONDS + 10) do
        task.wait(0.05)
    end
end

waitForInitialPopulateAndFake()

-- now it's safe to show main GUI
win.Window.Visible = true

-- stop loading animation and destroy loading GUI
loadingRunning = false
pcall(function() LoadingGUI:Destroy() end)

-- Real-time shop monitoring system with event-driven detection
local ShopMonitor = {
    lastUpdate = 0,
    updateThrottle = 0.1, -- Minimum time between updates (100ms)
    lastStockHash = {
        seeds = "",
        gear = "",
        petEggs = ""
    },
    connections = {},
    isActive = true
}

-- Efficient hash function for detecting changes
local function hashTable(tbl)
    local items = {}
    for k, v in pairs(tbl) do
        table.insert(items, k .. ":" .. tostring(v))
    end
    table.sort(items)
    return table.concat(items, "|")
end

-- Enhanced stock readers with change detection - always force fresh scan
local function GetSeedStockWithChangeDetection()
    local newStock = GetSeedStock() -- This now always scans fresh
    local newHash = hashTable(newStock)

    if newHash ~= ShopMonitor.lastStockHash.seeds then
        ShopMonitor.lastStockHash.seeds = newHash
        return newStock, true -- Stock changed
    end
    return newStock, false -- No change
end

local function GetGearStockWithChangeDetection()
    local newStock = GetGearStock() -- This now always scans fresh
    local newHash = hashTable(newStock)

    if newHash ~= ShopMonitor.lastStockHash.gear then
        ShopMonitor.lastStockHash.gear = newHash
        return newStock, true -- Stock changed
    end
    return newStock, false -- No change
end

local function GetPetEggStockWithChangeDetection()
    local newStock = GetPetEggStock() -- This now always scans fresh
    local newHash = hashTable(newStock)

    if newHash ~= ShopMonitor.lastStockHash.petEggs then
        ShopMonitor.lastStockHash.petEggs = newHash
        return newStock, true -- Stock changed
    end
    return newStock, false -- No change
end

-- Optimized combo refresh function
local function refreshComboIfNeeded(combo, stockChanged)
    if not combo or not stockChanged then return end

    pcall(function()
        local canPopulate = true
        if combo.IsPopulating and combo.IsPopulating() then canPopulate = false end
        if combo.LastPopulate and (tick() - combo.LastPopulate()) < 0.8 then canPopulate = false end

        if not combo.Dropdown.Visible then
            combo.Populate()
        elseif canPopulate then
            combo.Populate()
        end

        if combo.UpdateLabel then combo.UpdateLabel() end
    end)
end

-- Real-time monitoring function with throttling
local function updateShopData()
    local currentTime = tick()

    -- Throttle updates to prevent excessive processing
    if currentTime - ShopMonitor.lastUpdate < ShopMonitor.updateThrottle then
        return
    end

    ShopMonitor.lastUpdate = currentTime

    pcall(function()
        local shopContent = tabContents.Shop
        if not shopContent then return end

        -- Check each shop type for changes
        local _, seedsChanged = GetSeedStockWithChangeDetection()
        local _, gearChanged = GetGearStockWithChangeDetection()
        local _, petEggsChanged = GetPetEggStockWithChangeDetection()

        -- Only refresh UI if there were actual changes
        if seedsChanged then
            refreshComboIfNeeded(shopContent.SeedCombo, true)
        end

        if gearChanged then
            refreshComboIfNeeded(shopContent.GearCombo, true)
        end

        if petEggsChanged then
            refreshComboIfNeeded(shopContent.PetEggCombo, true)
        end
    end)
end

-- Event-driven shop monitoring using RunService.Heartbeat
ShopMonitor.connections.heartbeat = RunService.Heartbeat:Connect(function()
    if not ShopMonitor.isActive then return end
    updateShopData()
end)

-- Additional monitoring for shop UI visibility changes
local function monitorShopVisibility()
    local function checkShopUI(shopName, callback)
        local shop = PlayerGui:FindFirstChild(shopName)
        if shop then
            local connection
            connection = shop.AncestryChanged:Connect(function()
                if shop.Parent then
                    -- Shop became visible, trigger immediate update
                    task.spawn(callback)
                end
            end)
            table.insert(ShopMonitor.connections, connection)
        end
    end

    -- Monitor each shop type
    checkShopUI("Seed_Shop", function() GetSeedStockWithChangeDetection() end)
    checkShopUI("Gear_Shop", function() GetGearStockWithChangeDetection() end)
    checkShopUI("PetShop_UI", function() GetPetEggStockWithChangeDetection() end)
end

-- Initialize shop monitoring
monitorShopVisibility()

-- Cleanup function for when the script is destroyed
local function cleanup()
    ShopMonitor.isActive = false
    AutoFarmSystem.isActive = false
    AutoPlantSystem.isActive = false

    -- Disconnect AutoFarmHeartbeat connection
    if AutoFarmHeartbeat and AutoFarmHeartbeat.connection then
        AutoFarmHeartbeat.connection:Disconnect()
        AutoFarmHeartbeat.connection = nil
    end

    for _, connection in pairs(ShopMonitor.connections) do
        if connection and connection.Connected then
            connection:Disconnect()
        end
    end
    for _, connection in pairs(AutoFarmSystem.connections) do
        if connection and connection.Connected then
            connection:Disconnect()
        end
    end
    for _, connection in pairs(AutoPlantSystem.connections) do
        if connection and connection.Connected then
            connection:Disconnect()
        end
    end
    ShopMonitor.connections = {}
    AutoFarmSystem.connections = {}
    AutoPlantSystem.connections = {}
end

-- Connect cleanup to GUI destruction
UI.AncestryChanged:Connect(function()
    if not UI.Parent then
        cleanup()
    end
end)

-- Fallback periodic check (much less frequent) for edge cases
task.spawn(function()
    while ShopMonitor.isActive do
        task.wait(5) -- Only check every 5 seconds as fallback
        if ShopMonitor.isActive then
            pcall(function()
                -- Force a check regardless of throttling for fallback
                local oldThrottle = ShopMonitor.updateThrottle
                ShopMonitor.updateThrottle = 0
                updateShopData()
                ShopMonitor.updateThrottle = oldThrottle
            end)
        end
    end
end)

-- Ultra-fast continuous scanning function for instant collection
local function ContinuousScanAndHarvest()
    if not AutoFarmSystem.isActive then return end

    local currentTime = tick()
    local scanStartTime = currentTime

    -- Very minimal throttling for continuous collection
    if currentTime - AutoFarmHeartbeat.lastScanTime < AutoFarmHeartbeat.scanThrottle then
        return
    end

    AutoFarmHeartbeat.lastScanTime = currentTime
    AutoFarmSystem.lastScanTime = currentTime
    AutoFarmHeartbeat.frameProcessCount = 0 -- Reset frame counter

    -- Update performance stats
    AutoFarmHeartbeat.performanceStats.totalScans = AutoFarmHeartbeat.performanceStats.totalScans + 1

    pcall(function()
        -- Use the improved harvesting system from reference script
        local Plants = GetHarvestablePlants(true) -- Ignore distance for auto collect

        -- Process ALL plants immediately for continuous collection
        for _, Plant in pairs(Plants) do
            if not AutoFarmSystem.isActive then break end

            AutoFarmHeartbeat.frameProcessCount = AutoFarmHeartbeat.frameProcessCount + 1

            -- Minimal throttling for instant harvesting
            local now = tick()
            if now - AutoFarmHeartbeat.lastHarvestTime >= AutoFarmHeartbeat.harvestThrottle then
                local harvestSuccess = false
                pcall(function()
                    harvestSuccess = HarvestPlant(Plant)
                end)

                if harvestSuccess then
                    AutoFarmHeartbeat.lastHarvestTime = now
                    AutoFarmHeartbeat.performanceStats.totalHarvests = AutoFarmHeartbeat.performanceStats.totalHarvests + 1
                end
            end
        end



        -- Update performance statistics
        local scanDuration = tick() - scanStartTime
        local stats = AutoFarmHeartbeat.performanceStats
        stats.avgScanTime = (stats.avgScanTime * (stats.totalScans - 1) + scanDuration) / stats.totalScans

        -- Log performance every 30 seconds for monitoring
        if currentTime - stats.lastPerformanceCheck > 30 then
            stats.lastPerformanceCheck = currentTime
            print("Auto Collect Performance: Avg scan time:", string.format("%.3f", stats.avgScanTime * 1000), "ms, Total harvests:", stats.totalHarvests)
        end
    end)
end

-- Connect continuous auto farm to RunService.Heartbeat
local function startContinuousAutoFarm()
    if AutoFarmHeartbeat.connection then
        AutoFarmHeartbeat.connection:Disconnect()
    end

    -- Primary harvesting connection (every frame)
    AutoFarmHeartbeat.connection = RunService.Heartbeat:Connect(function()
        ContinuousScanAndHarvest()
        -- Also run auto plant system
        if AutoPlantSystem.isActive then
            pcall(AutoPlantLoop)
        end
    end)

    -- Secondary ultra-fast harvesting connection for instant collection
    RunService.RenderStepped:Connect(function()
        if not AutoFarmSystem.isActive then return end

        pcall(function()
            local Plants = GetHarvestablePlants(true)
            -- Instantly harvest any available plants
            for _, Plant in pairs(Plants) do
                if not AutoFarmSystem.isActive then break end
                pcall(function()
                    local success = HarvestPlant(Plant)
                    if success then
                        AutoFarmHeartbeat.performanceStats.totalHarvests = AutoFarmHeartbeat.performanceStats.totalHarvests + 1
                    end
                end)
            end
        end)
    end)
end

-- Start the continuous auto farm system
startContinuousAutoFarm()

-- Additional ultra-fast harvesting loop for maximum collection speed
task.spawn(function()
    while true do
        task.wait(0.001) -- Run every 1ms for instant collection

        if AutoFarmSystem.isActive then
            pcall(function()
                local Plants = GetHarvestablePlants(true)

                -- Harvest up to 10 plants per cycle for maximum speed
                local harvested = 0
                for _, Plant in pairs(Plants) do
                    if harvested >= 10 then break end
                    if not AutoFarmSystem.isActive then break end

                    local success = HarvestPlant(Plant)
                    if success then
                        harvested = harvested + 1
                        AutoFarmHeartbeat.performanceStats.totalHarvests = AutoFarmHeartbeat.performanceStats.totalHarvests + 1
                    end
                end
            end)
        end
    end
end)

-- auto-buy and auto-sell loops
task.spawn(function()
    while task.wait(1) do
        local shopContent = tabContents.Shop
        local miscContent = tabContents.Miscellaneous

        if shopContent then
            -- Auto-buy seeds if enabled
            if shopContent.SeedAutoState and shopContent.SeedAutoState.Value then
                pcall(BuyAllSelectedSeeds)
            end
            -- Auto-buy gear if enabled
            if shopContent.GearAutoState and shopContent.GearAutoState.Value then
                pcall(BuyAllSelectedGear)
            end
            -- Auto-buy pet eggs if enabled
            if shopContent.PetEggAutoState and shopContent.PetEggAutoState.Value then
                pcall(BuyAllSelectedPetEggs)
            end
        end

        -- Auto-sell if enabled
        if miscContent and miscContent.AutoSellState and miscContent.AutoSellState.Value then
            local crops = GetInvCrops()
            if #crops >= 15 then -- Sell when 15+ crops
                pcall(SellInventory)
            end
        end
    end
end)